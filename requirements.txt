# OpenTTD Bot Dependencies
# Hlavní knihovna pro komunikaci s OpenTTD serverem
pyOpenTTDAdmin==1.0.2

# Async knihovny (pokud nejsou součástí standardní knihovny)
asyncio-mqtt>=0.11.0  # Pro případné MQTT rozšíření
aiofiles>=0.8.0       # Pro async práci se soubory

# Logging a monitoring
colorlog>=6.7.0       # <PERSON><PERSON>ný logging
psutil>=5.9.0         # Monitoring systému

# Utility knihovny
python-dateutil>=2.8.2  # Práce s datumy
pytz>=2022.7.1          # Časové zóny

# Development dependencies (volitelné)
pytest>=7.2.0          # Testování
pytest-asyncio>=0.20.3 # Async testování
black>=22.12.0         # Code formatting
flake8>=6.0.0          # Linting

# Bezpečnost a konfigurace
python-dotenv>=0.21.0  # Pro .env soubory (volitelné)
cryptography>=38.0.4   # Pro případné šif<PERSON>í

# Databáze (volitelné pro pokročilé funkce)
aiosqlite>=0.18.0      # Async SQLite databáze

# Web interface (volitelné pro budoucí rozšíření)
aiohttp>=3.8.3         # Async HTTP server
jinja2>=3.1.2          # Template engine
