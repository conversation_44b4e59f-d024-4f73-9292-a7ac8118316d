#!/usr/bin/env python3
"""
Rychlý test připojení k OpenTTD serveru
"""

import asyncio
import sys
from aiopyopenttdadmin import Admin
import config

async def quick_test():
    print(f"🔍 Zkouším připojení k {config.SERVER_IP}:{config.ADMIN_PORT}")
    print(f"Bot: {config.BOT_NAME}")
    print(f"Heslo: {'*' * len(config.ADMIN_PASSWORD)}")
    print()
    
    try:
        # Zkusíme se připojit
        admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
        print("📡 Připojuji se...")
        
        # Pokusíme se přihlásit
        await admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)
        print("✅ Úspěšně přihlášen!")
        
        # Zkusíme odeslat zprávu
        await admin.send_global("🤖 Test bota - připojení funguje!")
        print("✅ Zpráva odeslána!")
        
        print("🎉 Vše funguje! Můžete spustit hlavního bota.")
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        print()
        print("💡 Možná řešení:")
        print("1. Zkontrolujte, že server běží")
        print("2. Ověřte heslo v config.py")
        print("3. Možná server nepovoluje admin připojení")
        print("4. Zkuste jiný port (např. změňte ADMIN_PORT v config.py)")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(quick_test())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Přerušeno")
        sys.exit(1)
