"""
Konfigurační soubor pro OpenTTD bota
"""

# Server nastavení
SERVER_IP = "************"
SERVER_PORT = 1500  # Hlavní port serveru
ADMIN_PORT = 1500   # Admin port pro bota (zkusíme hlavní port)
ADMIN_PASSWORD = "666"

# Bot nastavení
BOT_NAME = "robo-stepan_valic"
BOT_VERSION = "1.0.0"

# Logging nastavení
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Chat nastavení
COMMAND_PREFIX = "!"
ENABLE_ECHO = False  # Pokud True, bot bude opakovat všechny zprávy
ENABLE_COMMANDS = True

# Admin příkazy - seznam admin příkazů které bot může vykonávat
ADMIN_COMMANDS = [
    "kick",
    "ban", 
    "pause",
    "unpause",
    "save",
    "load",
    "reset_company",
    "server_info",
    "client_info",
    "company_info"
]

# Automatické zprávy
AUTO_MESSAGES = [
    "Vítejte na serveru! Použijte !help pro seznam příkazů.",
    "Nezapomeňte si uložit hru před odchodem!",
    "Pro pomoc napište !help"
]

AUTO_MESSAGE_INTERVAL = 300  # sekund (5 minut)

# Monitoring nastavení
MONITOR_CLIENTS = True
MONITOR_COMPANIES = True
MONITOR_ECONOMY = True

# Reconnect nastavení
AUTO_RECONNECT = True
RECONNECT_DELAY = 5  # sekund
MAX_RECONNECT_ATTEMPTS = 10
