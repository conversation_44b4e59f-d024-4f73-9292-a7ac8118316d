#!/usr/bin/env python3
"""
OpenTTD Bot pro server 2.56.244.242
Autor: AI Assistant
Verze: 1.0.0
"""

import asyncio
import logging
import time
import signal
import sys
from datetime import datetime
from typing import Dict, List

from aiopyopenttdadmin import Admin, AdminUpdateType, openttdpacket
import config

# Nastavení loggingu
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[
        logging.FileHandler('openttd_bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OpenTTDBot:
    def __init__(self):
        self.admin = None
        self.running = False
        self.clients: Dict[int, dict] = {}
        self.companies: Dict[int, dict] = {}
        self.last_auto_message = 0
        self.reconnect_attempts = 0
        
    async def start(self):
        """Spustí bota a připojí se k serveru"""
        logger.info(f"Spouštím {config.BOT_NAME} v{config.BOT_VERSION}")
        logger.info(f"Připojuji se k {config.SERVER_IP}:{config.ADMIN_PORT}")
        
        while self.reconnect_attempts < config.MAX_RECONNECT_ATTEMPTS:
            try:
                self.admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
                await self.admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)
                
                # Přihlášení k odběru událostí
                await self.admin.subscribe(AdminUpdateType.CHAT)
                await self.admin.subscribe(AdminUpdateType.CLIENT_INFO)
                await self.admin.subscribe(AdminUpdateType.CLIENT_JOIN)
                await self.admin.subscribe(AdminUpdateType.CLIENT_QUIT)
                await self.admin.subscribe(AdminUpdateType.COMPANY_INFO)
                await self.admin.subscribe(AdminUpdateType.COMPANY_NEW)
                await self.admin.subscribe(AdminUpdateType.COMPANY_REMOVE)
                
                # Registrace handlerů
                self.register_handlers()
                
                logger.info("Úspěšně připojen k serveru!")
                self.running = True
                self.reconnect_attempts = 0
                
                # Spuštění hlavní smyčky
                await self.run_main_loop()
                
            except Exception as e:
                logger.error(f"Chyba při připojování: {e}")
                self.reconnect_attempts += 1
                if self.reconnect_attempts < config.MAX_RECONNECT_ATTEMPTS:
                    logger.info(f"Pokus o reconnect za {config.RECONNECT_DELAY} sekund... ({self.reconnect_attempts}/{config.MAX_RECONNECT_ATTEMPTS})")
                    await asyncio.sleep(config.RECONNECT_DELAY)
                else:
                    logger.error("Maximální počet pokusů o reconnect dosažen. Ukončuji.")
                    break
    
    def register_handlers(self):
        """Registruje handlery pro různé typy paketů"""
        
        @self.admin.add_handler(openttdpacket.ChatPacket)
        async def handle_chat(admin: Admin, packet: openttdpacket.ChatPacket):
            await self.handle_chat_message(packet)
        
        @self.admin.add_handler(openttdpacket.ClientJoinPacket)
        async def handle_client_join(admin: Admin, packet: openttdpacket.ClientJoinPacket):
            await self.handle_client_join(packet)
        
        @self.admin.add_handler(openttdpacket.ClientQuitPacket)
        async def handle_client_quit(admin: Admin, packet: openttdpacket.ClientQuitPacket):
            await self.handle_client_quit(packet)
        
        @self.admin.add_handler(openttdpacket.ClientInfoPacket)
        async def handle_client_info(admin: Admin, packet: openttdpacket.ClientInfoPacket):
            await self.handle_client_info(packet)
    
    async def handle_chat_message(self, packet):
        """Zpracuje chat zprávu"""
        try:
            message = packet.message.strip()
            client_id = packet.id
            
            logger.info(f"Chat [{client_id}]: {message}")
            
            # Echo funkce
            if config.ENABLE_ECHO and not message.startswith(config.COMMAND_PREFIX):
                await self.admin.send_global(f"Echo: {message}")
            
            # Zpracování příkazů
            if config.ENABLE_COMMANDS and message.startswith(config.COMMAND_PREFIX):
                await self.handle_command(message, client_id)
                
        except Exception as e:
            logger.error(f"Chyba při zpracování chat zprávy: {e}")
    
    async def handle_command(self, message: str, client_id: int):
        """Zpracuje příkaz od uživatele"""
        try:
            parts = message[1:].split()  # Odstraní prefix a rozdělí
            if not parts:
                return
            
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []
            
            if command == "help":
                await self.cmd_help()
            elif command == "time":
                await self.cmd_time()
            elif command == "players":
                await self.cmd_players()
            elif command == "companies":
                await self.cmd_companies()
            elif command == "ping":
                await self.cmd_ping()
            elif command in config.ADMIN_COMMANDS:
                await self.handle_admin_command(command, args, client_id)
            else:
                await self.admin.send_global(f"Neznámý příkaz: {command}. Použijte !help pro seznam příkazů.")
                
        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu: {e}")
    
    async def cmd_help(self):
        """Zobrazí nápovědu"""
        help_text = f"""
Dostupné příkazy (prefix: {config.COMMAND_PREFIX}):
!help - Zobrazí tuto nápovědu
!time - Zobrazí aktuální čas
!ping - Test odezvy bota
!players - Seznam připojených hráčů
!companies - Seznam společností
Admin příkazy: {', '.join(config.ADMIN_COMMANDS)}
        """.strip()
        await self.admin.send_global(help_text)
    
    async def cmd_time(self):
        """Zobrazí aktuální čas"""
        current_time = datetime.now().strftime("%H:%M:%S %d.%m.%Y")
        await self.admin.send_global(f"Aktuální čas: {current_time}")
    
    async def cmd_ping(self):
        """Test odezvy"""
        await self.admin.send_global("Pong! Bot je online.")
    
    async def cmd_players(self):
        """Seznam připojených hráčů"""
        if not self.clients:
            await self.admin.send_global("Žádní hráči nejsou připojeni.")
        else:
            players = [f"ID {cid}: {info.get('name', 'Neznámý')}" for cid, info in self.clients.items()]
            await self.admin.send_global(f"Připojení hráči ({len(players)}): {', '.join(players)}")
    
    async def cmd_companies(self):
        """Seznam společností"""
        if not self.companies:
            await self.admin.send_global("Žádné společnosti neexistují.")
        else:
            companies = [f"ID {cid}: {info.get('name', 'Neznámá')}" for cid, info in self.companies.items()]
            await self.admin.send_global(f"Společnosti ({len(companies)}): {', '.join(companies)}")
    
    async def handle_admin_command(self, command: str, args: List[str], client_id: int):
        """Zpracuje admin příkaz"""
        # Zde by měla být kontrola, zda má uživatel admin práva
        # Pro jednoduchost nyní povolíme všem
        
        try:
            if command == "pause":
                await self.admin.send_rcon("pause")
                await self.admin.send_global("Hra pozastavena.")
            elif command == "unpause":
                await self.admin.send_rcon("unpause")
                await self.admin.send_global("Hra obnovena.")
            elif command == "save":
                save_name = args[0] if args else "autosave_bot"
                await self.admin.send_rcon(f"save {save_name}")
                await self.admin.send_global(f"Hra uložena jako: {save_name}")
            elif command == "server_info":
                await self.admin.send_rcon("info")
            else:
                await self.admin.send_global(f"Admin příkaz '{command}' není implementován.")
                
        except Exception as e:
            logger.error(f"Chyba při vykonávání admin příkazu {command}: {e}")
            await self.admin.send_global(f"Chyba při vykonávání příkazu: {command}")
    
    async def handle_client_join(self, packet):
        """Zpracuje připojení klienta"""
        try:
            client_id = packet.id
            logger.info(f"Klient {client_id} se připojil")
            await self.admin.send_global(f"Vítejte na serveru! Použijte !help pro nápovědu.")
        except Exception as e:
            logger.error(f"Chyba při zpracování připojení klienta: {e}")
    
    async def handle_client_quit(self, packet):
        """Zpracuje odpojení klienta"""
        try:
            client_id = packet.id
            if client_id in self.clients:
                name = self.clients[client_id].get('name', 'Neznámý')
                del self.clients[client_id]
                logger.info(f"Klient {client_id} ({name}) se odpojil")
        except Exception as e:
            logger.error(f"Chyba při zpracování odpojení klienta: {e}")
    
    async def handle_client_info(self, packet):
        """Zpracuje informace o klientovi"""
        try:
            self.clients[packet.id] = {
                'name': packet.name,
                'company': packet.company,
                'join_date': datetime.now()
            }
        except Exception as e:
            logger.error(f"Chyba při zpracování info o klientovi: {e}")
    
    async def send_auto_messages(self):
        """Odesílá automatické zprávy"""
        if not config.AUTO_MESSAGES:
            return
            
        current_time = time.time()
        if current_time - self.last_auto_message >= config.AUTO_MESSAGE_INTERVAL:
            import random
            message = random.choice(config.AUTO_MESSAGES)
            await self.admin.send_global(message)
            self.last_auto_message = current_time
    
    async def run_main_loop(self):
        """Hlavní smyčka bota"""
        try:
            while self.running:
                # Automatické zprávy
                await self.send_auto_messages()
                
                # Krátká pauza
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Přerušeno uživatelem")
        except Exception as e:
            logger.error(f"Chyba v hlavní smyčce: {e}")
        finally:
            await self.stop()
    
    async def stop(self):
        """Zastaví bota"""
        logger.info("Zastavuji bota...")
        self.running = False
        if self.admin:
            try:
                await self.admin.send_global("Bot se odpojuje...")
                # Zde by mělo být správné odpojení, ale knihovna to možná neimplementuje
            except:
                pass

# Hlavní funkce
async def main():
    bot = OpenTTDBot()
    
    # Graceful shutdown
    def signal_handler(signum, frame):
        logger.info("Přijat signál k ukončení")
        bot.running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    await bot.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot ukončen")
    except Exception as e:
        logger.error(f"Kritická chyba: {e}")
        sys.exit(1)
