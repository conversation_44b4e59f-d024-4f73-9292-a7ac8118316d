#!/usr/bin/env python3
"""
OpenTTD Bot pro server 2.56.244.242
Autor: AI Assistant
Verze: 1.0.0
"""

import logging
import time
import signal
import sys
import threading
from datetime import datetime
from typing import Dict, List

from pyopenttdadmin import Admin, AdminUpdateType, openttdpacket
import config

# Nastavení loggingu
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[
        logging.FileHandler('openttd_bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OpenTTDBot:
    def __init__(self):
        self.admin = None
        self.running = False
        self.clients: Dict[int, dict] = {}
        self.companies: Dict[int, dict] = {}
        self.last_auto_message = 0
        self.reconnect_attempts = 0

    def start(self):
        """Spustí bota a připojí se k serveru"""
        logger.info(f"Spouštím {config.BOT_NAME} v{config.BOT_VERSION}")
        logger.info(f"Připojuji se k {config.SERVER_IP}:{config.ADMIN_PORT}")

        while self.reconnect_attempts < config.MAX_RECONNECT_ATTEMPTS:
            try:
                self.admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
                self.admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)

                # Přihlášení k odběru událostí
                self.admin.subscribe(AdminUpdateType.CHAT)
                self.admin.subscribe(AdminUpdateType.CLIENT_INFO)
                self.admin.subscribe(AdminUpdateType.COMPANY_INFO)

                # Registrace handlerů
                self.register_handlers()

                logger.info("Úspěšně připojen k serveru!")
                self.running = True
                self.reconnect_attempts = 0

                # Spuštění hlavní smyčky
                self.run_main_loop()

            except Exception as e:
                logger.error(f"Chyba při připojování: {e}")
                self.reconnect_attempts += 1
                if self.reconnect_attempts < config.MAX_RECONNECT_ATTEMPTS:
                    logger.info(f"Pokus o reconnect za {config.RECONNECT_DELAY} sekund... ({self.reconnect_attempts}/{config.MAX_RECONNECT_ATTEMPTS})")
                    time.sleep(config.RECONNECT_DELAY)
                else:
                    logger.error("Maximální počet pokusů o reconnect dosažen. Ukončuji.")
                    break
    
    def register_handlers(self):
        """Registruje handlery pro různé typy paketů"""

        @self.admin.add_handler(openttdpacket.ChatPacket)
        def handle_chat(admin: Admin, packet: openttdpacket.ChatPacket):
            self.handle_chat_message(packet)

        @self.admin.add_handler(openttdpacket.ClientJoinPacket)
        def handle_client_join(admin: Admin, packet: openttdpacket.ClientJoinPacket):
            self.handle_client_join(packet)

        @self.admin.add_handler(openttdpacket.ClientQuitPacket)
        def handle_client_quit(admin: Admin, packet: openttdpacket.ClientQuitPacket):
            self.handle_client_quit(packet)

        @self.admin.add_handler(openttdpacket.ClientInfoPacket)
        def handle_client_info(admin: Admin, packet: openttdpacket.ClientInfoPacket):
            self.handle_client_info(packet)
    
    def handle_chat_message(self, packet):
        """Zpracuje chat zprávu"""
        try:
            message = packet.message.strip()
            client_id = packet.id

            logger.info(f"Chat [{client_id}]: {message}")

            # Echo funkce
            if config.ENABLE_ECHO and not message.startswith(config.COMMAND_PREFIX):
                self.admin.send_global(f"Echo: {message}")

            # Zpracování příkazů
            if config.ENABLE_COMMANDS and message.startswith(config.COMMAND_PREFIX):
                self.handle_command(message, client_id)

        except Exception as e:
            logger.error(f"Chyba při zpracování chat zprávy: {e}")

    def handle_command(self, message: str, client_id: int):
        """Zpracuje příkaz od uživatele"""
        try:
            parts = message[1:].split()  # Odstraní prefix a rozdělí
            if not parts:
                return

            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []

            if command == "help":
                self.cmd_help()
            elif command == "time":
                self.cmd_time()
            elif command == "players":
                self.cmd_players()
            elif command == "companies":
                self.cmd_companies()
            elif command == "ping":
                self.cmd_ping()
            elif command in config.ADMIN_COMMANDS:
                self.handle_admin_command(command, args, client_id)
            else:
                self.admin.send_global(f"Neznámý příkaz: {command}. Použijte !help pro seznam příkazů.")

        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu: {e}")
    
    def cmd_help(self):
        """Zobrazí nápovědu"""
        help_text = f"""
Dostupné příkazy (prefix: {config.COMMAND_PREFIX}):
!help - Zobrazí tuto nápovědu
!time - Zobrazí aktuální čas
!ping - Test odezvy bota
!players - Seznam připojených hráčů
!companies - Seznam společností
Admin příkazy: {', '.join(config.ADMIN_COMMANDS)}
        """.strip()
        self.admin.send_global(help_text)

    def cmd_time(self):
        """Zobrazí aktuální čas"""
        current_time = datetime.now().strftime("%H:%M:%S %d.%m.%Y")
        self.admin.send_global(f"Aktuální čas: {current_time}")

    def cmd_ping(self):
        """Test odezvy"""
        self.admin.send_global("Pong! Bot je online.")

    def cmd_players(self):
        """Seznam připojených hráčů"""
        if not self.clients:
            self.admin.send_global("Žádní hráči nejsou připojeni.")
        else:
            players = [f"ID {cid}: {info.get('name', 'Neznámý')}" for cid, info in self.clients.items()]
            self.admin.send_global(f"Připojení hráči ({len(players)}): {', '.join(players)}")

    def cmd_companies(self):
        """Seznam společností"""
        if not self.companies:
            self.admin.send_global("Žádné společnosti neexistují.")
        else:
            companies = [f"ID {cid}: {info.get('name', 'Neznámá')}" for cid, info in self.companies.items()]
            self.admin.send_global(f"Společnosti ({len(companies)}): {', '.join(companies)}")
    
    def handle_admin_command(self, command: str, args: List[str], client_id: int):
        """Zpracuje admin příkaz"""
        # Zde by měla být kontrola, zda má uživatel admin práva
        # Pro jednoduchost nyní povolíme všem

        try:
            if command == "pause":
                self.admin.send_rcon("pause")
                self.admin.send_global("Hra pozastavena.")
            elif command == "unpause":
                self.admin.send_rcon("unpause")
                self.admin.send_global("Hra obnovena.")
            elif command == "save":
                save_name = args[0] if args else "autosave_bot"
                self.admin.send_rcon(f"save {save_name}")
                self.admin.send_global(f"Hra uložena jako: {save_name}")
            elif command == "server_info":
                self.admin.send_rcon("info")
            else:
                self.admin.send_global(f"Admin příkaz '{command}' není implementován.")

        except Exception as e:
            logger.error(f"Chyba při vykonávání admin příkazu {command}: {e}")
            self.admin.send_global(f"Chyba při vykonávání příkazu: {command}")
    
    def handle_client_join(self, packet):
        """Zpracuje připojení klienta"""
        try:
            client_id = packet.id
            logger.info(f"Klient {client_id} se připojil")
            self.admin.send_global(f"Vítejte na serveru! Použijte !help pro nápovědu.")
        except Exception as e:
            logger.error(f"Chyba při zpracování připojení klienta: {e}")

    def handle_client_quit(self, packet):
        """Zpracuje odpojení klienta"""
        try:
            client_id = packet.id
            if client_id in self.clients:
                name = self.clients[client_id].get('name', 'Neznámý')
                del self.clients[client_id]
                logger.info(f"Klient {client_id} ({name}) se odpojil")
        except Exception as e:
            logger.error(f"Chyba při zpracování odpojení klienta: {e}")

    def handle_client_info(self, packet):
        """Zpracuje informace o klientovi"""
        try:
            self.clients[packet.id] = {
                'name': packet.name,
                'company': packet.company,
                'join_date': datetime.now()
            }
        except Exception as e:
            logger.error(f"Chyba při zpracování info o klientovi: {e}")

    def send_auto_messages(self):
        """Odesílá automatické zprávy"""
        if not config.AUTO_MESSAGES:
            return

        current_time = time.time()
        if current_time - self.last_auto_message >= config.AUTO_MESSAGE_INTERVAL:
            import random
            message = random.choice(config.AUTO_MESSAGES)
            self.admin.send_global(message)
            self.last_auto_message = current_time
    
    def run_main_loop(self):
        """Hlavní smyčka bota"""
        try:
            # Spustíme admin.run() v separátním vlákně pro zpracování paketů
            def packet_handler():
                try:
                    self.admin.run()
                except Exception as e:
                    logger.error(f"Chyba v packet handleru: {e}")
                    self.running = False

            packet_thread = threading.Thread(target=packet_handler, daemon=True)
            packet_thread.start()

            # Hlavní smyčka pro automatické zprávy
            while self.running:
                # Automatické zprávy
                self.send_auto_messages()

                # Krátká pauza
                time.sleep(1)

        except KeyboardInterrupt:
            logger.info("Přerušeno uživatelem")
        except Exception as e:
            logger.error(f"Chyba v hlavní smyčce: {e}")
        finally:
            self.stop()

    def stop(self):
        """Zastaví bota"""
        logger.info("Zastavuji bota...")
        self.running = False
        if self.admin:
            try:
                self.admin.send_global("Bot se odpojuje...")
                # Zde by mělo být správné odpojení, ale knihovna to možná neimplementuje
            except:
                pass

# Hlavní funkce
def main():
    bot = OpenTTDBot()

    # Graceful shutdown
    def signal_handler(signum, frame):
        logger.info("Přijat signál k ukončení")
        bot.running = False
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    bot.start()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Bot ukončen")
    except Exception as e:
        logger.error(f"Kritická chyba: {e}")
        sys.exit(1)
