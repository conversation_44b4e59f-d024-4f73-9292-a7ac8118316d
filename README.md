# OpenTTD Bot 🚂

Pokročilý Python bot pro OpenTTD 14.1 server s admin funkcemi a chat příkazy.

## 📋 Funkce

- **Automatické připojení** k OpenTTD serveru
- **Chat příkazy** s prefixem `!`
- **Admin funkce** (pause, save, info, atd.)
- **Monitoring hráčů** a společností
- **<PERSON>k<PERSON> zprávy** v pravidelných intervalech
- **Auto-reconnect** při výpadku spojení
- **Detailní logging** všech aktivit

## 🚀 Rychlé spuštění

### 1. Příprava prostředí

```bash
# Aktivace virtuálního prostředí (už máte vytvořené)
source venv/bin/activate

# Instalace závislostí
pip install -r requirements.txt
```

### 2. Konfigurace

Bot je předkonfigurován pro váš server:
- **IP**: ************
- **Port**: 1500 (h<PERSON>n<PERSON>)
- **Admin port**: 1501
- **Admin heslo**: 666

Můžete upravit nastavení v souboru `config.py`.

### 3. Spuštění

```bash
# Spuštění bota
python main.py

# Nebo použijte spouštěcí skript
chmod +x run.sh
./run.sh
```

## 🎮 Dostupné příkazy

### Základní příkazy (pro všechny hráče)

| Příkaz | Popis |
|--------|-------|
| `!help` | Zobrazí seznam všech příkazů |
| `!time` | Zobrazí aktuální čas |
| `!ping` | Test odezvy bota |
| `!players` | Seznam připojených hráčů |
| `!companies` | Seznam existujících společností |

### Admin příkazy

| Příkaz | Popis | Příklad |
|--------|-------|---------|
| `!pause` | Pozastaví hru | `!pause` |
| `!unpause` | Obnoví hru | `!unpause` |
| `!save` | Uloží hru | `!save moje_hra` |
| `!server_info` | Informace o serveru | `!server_info` |

## ⚙️ Konfigurace

### config.py

```python
# Server nastavení
SERVER_IP = "************"
ADMIN_PORT = 1501
ADMIN_PASSWORD = "666"

# Bot nastavení
BOT_NAME = "OpenTTD-Bot"
COMMAND_PREFIX = "!"

# Automatické zprávy
AUTO_MESSAGES = [
    "Vítejte na serveru!",
    "Použijte !help pro nápovědu"
]
AUTO_MESSAGE_INTERVAL = 300  # 5 minut
```

## 📁 Struktura projektu

```
openttd-bot/
├── main.py           # Hlavní soubor bota
├── config.py         # Konfigurace
├── requirements.txt  # Python závislosti
├── README.md         # Dokumentace
├── run.sh           # Spouštěcí skript
├── openttd_bot.log  # Log soubor (vytvoří se automaticky)
└── venv/            # Virtuální prostředí
```

## 🔧 Pokročilé nastavení

### Logging

Bot automaticky loguje do:
- **Konzole** - pro okamžité sledování
- **Soubor** `openttd_bot.log` - pro archivaci

Úroveň logování můžete změnit v `config.py`:
```python
LOG_LEVEL = "DEBUG"  # DEBUG, INFO, WARNING, ERROR
```

### Automatické zprávy

Upravte v `config.py`:
```python
AUTO_MESSAGES = [
    "Vaše vlastní zpráva 1",
    "Vaše vlastní zpráva 2"
]
AUTO_MESSAGE_INTERVAL = 600  # 10 minut
```

### Reconnect nastavení

```python
AUTO_RECONNECT = True
RECONNECT_DELAY = 5
MAX_RECONNECT_ATTEMPTS = 10
```

## 🐛 Řešení problémů

### Bot se nemůže připojit

1. **Zkontrolujte síťové připojení**:
   ```bash
   ping ************
   ```

2. **Ověřte, že admin port je otevřený**:
   ```bash
   telnet 2.56.************
   ```

3. **Zkontrolujte heslo** v `config.py`

### Bot se odpojuje

- Zkontrolujte logy v `openttd_bot.log`
- Ověřte stabilitu internetového připojení
- Možná je server přetížený

### Příkazy nefungují

- Ujistěte se, že používáte správný prefix (`!`)
- Zkontrolujte, že je `ENABLE_COMMANDS = True` v config.py

## 📊 Monitoring

Bot sleduje:
- **Připojené hráče** - automaticky aktualizuje seznam
- **Společnosti** - sleduje vytváření/rušení
- **Chat zprávy** - loguje všechnu komunikaci
- **Admin příkazy** - zaznamenává všechny admin akce

## 🔒 Bezpečnost

- Admin příkazy jsou dostupné všem (můžete upravit v kódu)
- Hesla jsou uložena v plain textu (pro produkci doporučujeme šifrování)
- Bot loguje všechny aktivity

## 🚀 Rozšíření

Bot je navržen pro snadné rozšíření:

### Přidání nového příkazu

```python
async def cmd_muj_prikaz(self):
    """Můj vlastní příkaz"""
    await self.admin.send_global("Můj vlastní příkaz funguje!")

# Přidejte do handle_command():
elif command == "muj_prikaz":
    await self.cmd_muj_prikaz()
```

### Databáze

Pro ukládání dat můžete použít SQLite:
```python
import aiosqlite

async def save_to_db(self, data):
    async with aiosqlite.connect("bot_data.db") as db:
        await db.execute("INSERT INTO logs VALUES (?)", (data,))
        await db.commit()
```

## 📞 Podpora

Pro problémy nebo dotazy:
1. Zkontrolujte logy v `openttd_bot.log`
2. Ověřte konfiguraci v `config.py`
3. Restartujte bota pomocí `./run.sh`

## 📄 Licence

Tento projekt je poskytován "jak je" bez jakýchkoli záruk.

---

**Vytvořeno pro OpenTTD 14.1 server ************** 🚂
