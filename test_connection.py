#!/usr/bin/env python3
"""
Test skript pro ověření připojení k OpenTTD serveru
"""

import asyncio
import sys
import socket
from aiopyopenttdadmin import Admin
import config

async def test_basic_connection():
    """Test základního síťového připojení"""
    print("🔍 Testuji základní síťové připojení...")

    # Seznam portů k testování
    ports_to_test = [config.ADMIN_PORT, 1500, 1501, 3977, 3978, 3979]

    working_ports = []

    for port in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((config.SERVER_IP, port))
            sock.close()

            if result == 0:
                print(f"✅ Port {port} je otevřený na {config.SERVER_IP}")
                working_ports.append(port)
            else:
                print(f"❌ Port {port} nen<PERSON> dostupný na {config.SERVER_IP}")

        except Exception as e:
            print(f"❌ Chyba při testování portu {port}: {e}")

    if working_ports:
        print(f"🎯 Dostupné porty: {working_ports}")
        if config.ADMIN_PORT not in working_ports:
            print(f"⚠️  Doporučuji změnit ADMIN_PORT v config.py na jeden z dostupných portů")
        return True
    else:
        print("❌ Žádný port není dostupný")
        return False

async def test_admin_connection():
    """Test připojení k admin portu"""
    print("🔍 Testuji připojení k admin portu...")
    
    try:
        admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
        await admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)
        
        print("✅ Úspěšně připojen k admin portu!")
        print(f"   - Server: {config.SERVER_IP}:{config.ADMIN_PORT}")
        print(f"   - Bot: {config.BOT_NAME}")
        
        # Test odeslání zprávy
        await admin.send_global("🤖 Test připojení bota - ignorujte tuto zprávu")
        print("✅ Test zpráva odeslána!")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při připojování k admin portu: {e}")
        return False

async def test_subscriptions():
    """Test přihlášení k odběru událostí"""
    print("🔍 Testuji přihlášení k událostem...")
    
    try:
        from aiopyopenttdadmin import AdminUpdateType
        
        admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
        await admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)
        
        # Test různých subscriptions
        await admin.subscribe(AdminUpdateType.CHAT)
        print("✅ Přihlášen k chat událostem")
        
        await admin.subscribe(AdminUpdateType.CLIENT_INFO)
        print("✅ Přihlášen k client info")
        
        await admin.subscribe(AdminUpdateType.COMPANY_INFO)
        print("✅ Přihlášen k company info")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při přihlašování k událostem: {e}")
        return False

async def test_commands():
    """Test admin příkazů"""
    print("🔍 Testuji admin příkazy...")
    
    try:
        admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
        await admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)
        
        # Test RCON příkazu
        await admin.send_rcon("info")
        print("✅ RCON příkaz 'info' odeslán")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování příkazů: {e}")
        return False

def print_config_info():
    """Vypíše informace o konfiguraci"""
    print("📋 Konfigurace:")
    print(f"   - Server IP: {config.SERVER_IP}")
    print(f"   - Admin Port: {config.ADMIN_PORT}")
    print(f"   - Bot Name: {config.BOT_NAME}")
    print(f"   - Password: {'*' * len(config.ADMIN_PASSWORD)}")
    print()

async def main():
    """Hlavní test funkce"""
    print("🚂 OpenTTD Bot - Test připojení")
    print("=" * 40)
    
    print_config_info()
    
    # Postupné testování
    tests = [
        ("Základní připojení", test_basic_connection),
        ("Admin připojení", test_admin_connection),
        ("Přihlášení k událostem", test_subscriptions),
        ("Admin příkazy", test_commands)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📝 Test: {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Neočekávaná chyba v testu '{test_name}': {e}")
            results.append((test_name, False))
        
        # Krátká pauza mezi testy
        await asyncio.sleep(1)
    
    # Shrnutí výsledků
    print("\n" + "=" * 40)
    print("📊 Shrnutí testů:")
    print("-" * 20)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PROŠEL" if result else "❌ SELHAL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nVýsledek: {passed}/{total} testů prošlo")
    
    if passed == total:
        print("🎉 Všechny testy prošly! Bot by měl fungovat správně.")
        return 0
    else:
        print("⚠️  Některé testy selhaly. Zkontrolujte konfiguraci a síťové připojení.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test přerušen uživatelem")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Kritická chyba: {e}")
        sys.exit(1)
