#!/bin/bash

# OpenTTD Bot Spouště<PERSON><PERSON>t
# Autor: AI Assistant
# Verze: 1.0.0

set -e  # Ukon<PERSON>í při chybě

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkce pro barevný výstup
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════╗"
echo "║           OpenTTD Bot v1.0           ║"
echo "║        Server: ************          ║"
echo "╚══════════════════════════════════════╝"
echo -e "${NC}"

# Kontrola Python verze
print_info "Kontroluji Python verzi..."
if ! command -v python3 &> /dev/null; then
    print_error "Python3 není nainstalován!"
    exit 1
fi

PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
print_success "Python verze: $PYTHON_VERSION"

# Kontrola virtuálního prostředí
print_info "Kontroluji virtuální prostředí..."
if [ ! -d "venv" ]; then
    print_warning "Virtuální prostředí neexistuje, vytvářím..."
    python3 -m venv venv
    print_success "Virtuální prostředí vytvořeno"
fi

# Aktivace virtuálního prostředí
print_info "Aktivuji virtuální prostředí..."
source venv/bin/activate

# Kontrola a instalace závislostí
print_info "Kontroluji závislosti..."
if [ -f "requirements.txt" ]; then
    print_info "Instaluji/aktualizuji závislosti..."
    pip install -q --upgrade pip
    pip install -q -r requirements.txt
    print_success "Závislosti nainstalované"
else
    print_warning "requirements.txt nenalezen, instaluji pouze pyOpenTTDAdmin..."
    pip install -q pyOpenTTDAdmin
fi

# Kontrola konfigurace
print_info "Kontroluji konfiguraci..."
if [ ! -f "config.py" ]; then
    print_error "config.py nenalezen!"
    exit 1
fi

# Kontrola hlavního souboru
if [ ! -f "main.py" ]; then
    print_error "main.py nenalezen!"
    exit 1
fi

# Kontrola síťového připojení
print_info "Testuji připojení k serveru..."
if ping -c 1 -W 3 ************ &> /dev/null; then
    print_success "Server je dostupný"
else
    print_warning "Server neodpovídá na ping, ale zkusím se připojit..."
fi

# Vytvoření backup logu (pokud existuje)
if [ -f "openttd_bot.log" ]; then
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    mv openttd_bot.log "openttd_bot_${TIMESTAMP}.log"
    print_info "Starý log přejmenován na openttd_bot_${TIMESTAMP}.log"
fi

# Spuštění bota
print_info "Spouštím OpenTTD bota..."
print_info "Pro ukončení použijte Ctrl+C"
echo ""

# Trap pro graceful shutdown
trap 'print_info "Ukončuji bota..."; exit 0' INT TERM

# Spuštění s automatickým restartem
RESTART_COUNT=0
MAX_RESTARTS=5

while [ $RESTART_COUNT -lt $MAX_RESTARTS ]; do
    print_info "Pokus o spuštění #$((RESTART_COUNT + 1))"
    
    if python3 main.py; then
        print_success "Bot byl ukončen normálně"
        break
    else
        EXIT_CODE=$?
        print_error "Bot skončil s chybou (exit code: $EXIT_CODE)"
        
        if [ $RESTART_COUNT -lt $((MAX_RESTARTS - 1)) ]; then
            print_warning "Restartuji za 5 sekund..."
            sleep 5
            RESTART_COUNT=$((RESTART_COUNT + 1))
        else
            print_error "Dosažen maximální počet restartů ($MAX_RESTARTS)"
            break
        fi
    fi
done

print_info "Skript ukončen"
