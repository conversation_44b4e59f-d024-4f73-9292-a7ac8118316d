"""
Utility funkce pro OpenTTD bota
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class BotStats:
    """Třída pro sledování statistik bota"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.messages_sent = 0
        self.commands_processed = 0
        self.clients_seen = set()
        self.uptime_start = time.time()
        
    def add_message(self):
        """Přidá zprávu do statistik"""
        self.messages_sent += 1
        
    def add_command(self):
        """Přidá příkaz do statistik"""
        self.commands_processed += 1
        
    def add_client(self, client_id: int):
        """Přidá klienta do seznamu viděných"""
        self.clients_seen.add(client_id)
        
    def get_uptime(self) -> str:
        """Vrátí uptime jako string"""
        uptime_seconds = time.time() - self.uptime_start
        uptime_delta = timedelta(seconds=int(uptime_seconds))
        return str(uptime_delta)
        
    def get_stats_dict(self) -> Dict:
        """Vrátí statistiky jako slovník"""
        return {
            'start_time': self.start_time.isoformat(),
            'uptime': self.get_uptime(),
            'messages_sent': self.messages_sent,
            'commands_processed': self.commands_processed,
            'unique_clients': len(self.clients_seen),
            'clients_seen': list(self.clients_seen)
        }

class CommandCooldown:
    """Třída pro správu cooldownů příkazů"""
    
    def __init__(self):
        self.cooldowns: Dict[str, Dict[int, float]] = {}
        
    def is_on_cooldown(self, command: str, client_id: int, cooldown_time: float = 5.0) -> bool:
        """Zkontroluje, zda je příkaz na cooldownu"""
        if command not in self.cooldowns:
            self.cooldowns[command] = {}
            
        current_time = time.time()
        last_used = self.cooldowns[command].get(client_id, 0)
        
        if current_time - last_used < cooldown_time:
            return True
            
        self.cooldowns[command][client_id] = current_time
        return False
        
    def get_remaining_cooldown(self, command: str, client_id: int, cooldown_time: float = 5.0) -> float:
        """Vrátí zbývající čas cooldownu"""
        if command not in self.cooldowns:
            return 0.0
            
        current_time = time.time()
        last_used = self.cooldowns[command].get(client_id, 0)
        remaining = cooldown_time - (current_time - last_used)
        
        return max(0.0, remaining)

class MessageQueue:
    """Třída pro správu fronty zpráv"""
    
    def __init__(self, max_messages_per_second: float = 2.0):
        self.queue: List[str] = []
        self.last_send_time = 0.0
        self.min_interval = 1.0 / max_messages_per_second
        
    async def add_message(self, message: str):
        """Přidá zprávu do fronty"""
        self.queue.append(message)
        
    async def process_queue(self, send_function):
        """Zpracuje frontu zpráv s rate limitingem"""
        if not self.queue:
            return
            
        current_time = time.time()
        if current_time - self.last_send_time >= self.min_interval:
            message = self.queue.pop(0)
            await send_function(message)
            self.last_send_time = current_time

class ServerMonitor:
    """Třída pro monitoring serveru"""
    
    def __init__(self):
        self.server_info = {}
        self.last_update = 0
        self.update_interval = 60  # sekund
        
    def should_update(self) -> bool:
        """Zkontroluje, zda je čas na aktualizaci"""
        return time.time() - self.last_update >= self.update_interval
        
    def update_server_info(self, info: Dict):
        """Aktualizuje informace o serveru"""
        self.server_info = info
        self.last_update = time.time()
        
    def get_server_status(self) -> str:
        """Vrátí status serveru jako string"""
        if not self.server_info:
            return "Informace o serveru nejsou dostupné"
            
        return f"Server: {self.server_info.get('name', 'Neznámý')}, " \
               f"Hráči: {self.server_info.get('clients', 0)}, " \
               f"Společnosti: {self.server_info.get('companies', 0)}"

def format_time_duration(seconds: float) -> str:
    """Formátuje čas v sekundách na čitelný string"""
    if seconds < 60:
        return f"{int(seconds)}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}m {secs}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}h {minutes}m"

def validate_command_args(args: List[str], expected_count: int, command_name: str) -> Optional[str]:
    """Validuje argumenty příkazu"""
    if len(args) < expected_count:
        return f"Příkaz {command_name} vyžaduje {expected_count} argumentů, ale bylo zadáno {len(args)}"
    return None

def sanitize_message(message: str, max_length: int = 200) -> str:
    """Očistí a zkrátí zprávu"""
    # Odstraní nebezpečné znaky
    sanitized = ''.join(char for char in message if char.isprintable())
    
    # Zkrátí zprávu
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length-3] + "..."
        
    return sanitized

def parse_time_string(time_str: str) -> Optional[int]:
    """Parsuje časový string (např. '5m', '1h', '30s') na sekundy"""
    if not time_str:
        return None
        
    time_str = time_str.lower().strip()
    
    if time_str.endswith('s'):
        try:
            return int(time_str[:-1])
        except ValueError:
            return None
    elif time_str.endswith('m'):
        try:
            return int(time_str[:-1]) * 60
        except ValueError:
            return None
    elif time_str.endswith('h'):
        try:
            return int(time_str[:-1]) * 3600
        except ValueError:
            return None
    else:
        try:
            return int(time_str)
        except ValueError:
            return None

class ConfigManager:
    """Třída pro správu konfigurace"""
    
    def __init__(self, config_file: str = "bot_config.json"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
        
    def load_config(self):
        """Načte konfiguraci ze souboru"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            self.config = self.get_default_config()
            self.save_config()
        except json.JSONDecodeError:
            print(f"Chyba při načítání {self.config_file}, používám výchozí konfiguraci")
            self.config = self.get_default_config()
            
    def save_config(self):
        """Uloží konfiguraci do souboru"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Chyba při ukládání konfigurace: {e}")
            
    def get_default_config(self) -> Dict:
        """Vrátí výchozí konfiguraci"""
        return {
            "command_cooldowns": {
                "default": 5.0,
                "help": 10.0,
                "players": 15.0,
                "companies": 15.0
            },
            "rate_limiting": {
                "max_messages_per_second": 2.0,
                "burst_limit": 5
            },
            "auto_features": {
                "welcome_message": True,
                "goodbye_message": True,
                "periodic_stats": False
            }
        }
        
    def get(self, key: str, default=None):
        """Získá hodnotu z konfigurace"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
        
    def set(self, key: str, value):
        """Nastaví hodnotu v konfiguraci"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        config[keys[-1]] = value
        self.save_config()

# Utility funkce pro práci s OpenTTD daty
def parse_openttd_date(date_value: int) -> str:
    """Převede OpenTTD datum na čitelný string"""
    # OpenTTD používá dny od 1. ledna roku 0
    # Toto je zjednodušená verze
    year = date_value // 365
    day_of_year = date_value % 365
    month = (day_of_year // 30) + 1
    day = (day_of_year % 30) + 1
    
    return f"{day:02d}.{month:02d}.{year:04d}"

def format_money(amount: int) -> str:
    """Formátuje peníze do čitelného formátu"""
    if amount >= 1000000:
        return f"{amount / 1000000:.1f}M"
    elif amount >= 1000:
        return f"{amount / 1000:.1f}K"
    else:
        return str(amount)

# Konstanty pro OpenTTD
OPENTTD_CONSTANTS = {
    'MAX_COMPANIES': 15,
    'MAX_CLIENTS': 255,
    'INVALID_CLIENT': 0,
    'INVALID_COMPANY': 255,
    'SPECTATOR_COMPANY': 255
}
