#!/usr/bin/env python3
"""
Skript pro nalezení správného admin portu OpenTTD serveru
"""

import asyncio
import socket
import sys
from aiopyopenttdadmin import Admin
import config

async def test_admin_port(ip: str, port: int, password: str, bot_name: str):
    """Test konkrétního admin portu"""
    try:
        print(f"🔍 Testuji admin port {port}...")
        
        # Nejdříve test síťového připojení
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((ip, port))
        sock.close()
        
        if result != 0:
            print(f"❌ Port {port} není dostupný")
            return False
        
        # Test admin připojení
        admin = Admin(ip=ip, port=port)
        await admin.login(bot_name, password=password)
        
        # Zkusíme admin příkaz místo chat zprávy
        await admin.send_rcon("info")
        print(f"✅ Admin port {port} funguje!")
        
        return True
        
    except Exception as e:
        print(f"❌ Port {port} - chyba: {e}")
        return False

async def find_admin_port():
    """Najde správný admin port"""
    print("🔍 Hledám správný admin port pro OpenTTD server...")
    print(f"Server: {config.SERVER_IP}")
    print(f"Heslo: {'*' * len(config.ADMIN_PASSWORD)}")
    print(f"Bot: {config.BOT_NAME}")
    print()
    
    # Typické admin porty pro OpenTTD
    ports_to_test = [
        3977,  # Výchozí admin port OpenTTD
        3978,  # Další možný admin port
        1501,  # Váš původní admin port
        1502,  # Game port + 2
        1503,  # Game port + 3
        config.SERVER_PORT + 477,  # Game port + 477 (standardní offset)
    ]
    
    working_ports = []
    
    for port in ports_to_test:
        if await test_admin_port(config.SERVER_IP, port, config.ADMIN_PASSWORD, config.BOT_NAME):
            working_ports.append(port)
            
        # Krátká pauza mezi testy
        await asyncio.sleep(1)
    
    print()
    print("=" * 50)
    
    if working_ports:
        print(f"🎉 Nalezené funkční admin porty: {working_ports}")
        recommended_port = working_ports[0]
        print(f"💡 Doporučuji použít port: {recommended_port}")
        print()
        print("📝 Aktualizujte config.py:")
        print(f"ADMIN_PORT = {recommended_port}")
        
        return recommended_port
    else:
        print("❌ Žádný admin port nebyl nalezen")
        print()
        print("💡 Možné příčiny:")
        print("1. Server nemá povolený admin port")
        print("2. Špatné heslo")
        print("3. Server neběží")
        print("4. Firewall blokuje připojení")
        
        return None

async def test_game_vs_admin():
    """Test rozdílu mezi game a admin portem"""
    print("\n🔍 Testuji rozdíl mezi game a admin portem...")
    
    game_port = config.SERVER_PORT
    
    # Test game portu
    try:
        print(f"📡 Test game portu {game_port}...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((config.SERVER_IP, game_port))
        sock.close()
        
        if result == 0:
            print(f"✅ Game port {game_port} je dostupný")
        else:
            print(f"❌ Game port {game_port} není dostupný")
            
    except Exception as e:
        print(f"❌ Chyba při testování game portu: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(test_game_vs_admin())
        result = asyncio.run(find_admin_port())
        
        if result:
            print(f"\n🚀 Pro spuštění bota změňte ADMIN_PORT na {result} v config.py")
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Přerušeno")
        sys.exit(1)
