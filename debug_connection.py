#!/usr/bin/env python3
"""
Debug skript pro OpenTTD připojení
"""

import asyncio
import socket
import sys
from aiopyopenttdadmin import Admin
from pyopenttdadmin import Admin as SyncAdmin
import config

async def test_sync_vs_async():
    """Test synchronní vs asynchronn<PERSON> verze"""
    print("🔍 Testuji synchronní vs asynchronn<PERSON> připojení...")
    
    # Test synchronní verze
    print("\n📡 Test synchronní verze (pyopenttdadmin)...")
    try:
        with SyncAdmin(ip=config.SERVER_IP, port=config.ADMIN_PORT) as admin:
            admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)
            print("✅ Synchronní připojení funguje!")
            
            # Zkusíme odeslat zprávu
            admin.send_global("🤖 Test synchronního bota")
            print("✅ Synchronní zpráva odeslána!")
            return True
            
    except Exception as e:
        print(f"❌ Synchronní verze selhala: {e}")
    
    # Test asynchronní verze
    print("\n📡 Test asynchronní verze (aiopyopenttdadmin)...")
    try:
        admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
        await admin.login(config.BOT_NAME, password=config.ADMIN_PASSWORD)
        print("✅ Asynchronní připojení funguje!")
        
        # Zkusíme odeslat zprávu
        await admin.send_global("🤖 Test asynchronního bota")
        print("✅ Asynchronní zpráva odeslána!")
        return True
        
    except Exception as e:
        print(f"❌ Asynchronní verze selhala: {e}")
    
    return False

async def test_different_passwords():
    """Test různých hesel"""
    print("\n🔍 Testuji různá hesla...")
    
    passwords_to_test = [
        config.ADMIN_PASSWORD,  # Aktuální heslo
        "",                     # Prázdné heslo
        "admin",               # Výchozí heslo
        "password",            # Běžné heslo
        "openttd",             # OpenTTD heslo
    ]
    
    for password in passwords_to_test:
        try:
            print(f"🔑 Zkouším heslo: {'(prázdné)' if password == '' else '*' * len(password)}")
            
            admin = Admin(ip=config.SERVER_IP, port=config.ADMIN_PORT)
            await admin.login(config.BOT_NAME, password=password)
            
            print(f"✅ Heslo funguje: {password}")
            return password
            
        except Exception as e:
            print(f"❌ Heslo nefunguje: {e}")
    
    return None

async def test_raw_connection():
    """Test raw TCP připojení"""
    print("\n🔍 Testuji raw TCP připojení...")
    
    try:
        reader, writer = await asyncio.open_connection(config.SERVER_IP, config.ADMIN_PORT)
        print("✅ TCP připojení navázáno")
        
        # Zkusíme poslat nějaká data
        writer.write(b"test\n")
        await writer.drain()
        
        # Počkáme na odpověď
        try:
            data = await asyncio.wait_for(reader.read(100), timeout=3.0)
            print(f"📨 Přijata data: {data}")
        except asyncio.TimeoutError:
            print("⏰ Timeout - server neodpověděl")
        
        writer.close()
        await writer.wait_closed()
        
        return True
        
    except Exception as e:
        print(f"❌ Raw připojení selhalo: {e}")
        return False

async def scan_nearby_ports():
    """Skenuje porty kolem game portu"""
    print(f"\n🔍 Skenuji porty kolem {config.SERVER_PORT}...")
    
    base_port = config.SERVER_PORT
    ports_to_scan = []
    
    # Přidáme porty kolem base portu
    for offset in range(-5, 10):
        port = base_port + offset
        if 1 <= port <= 65535:
            ports_to_scan.append(port)
    
    open_ports = []
    
    for port in ports_to_scan:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((config.SERVER_IP, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} je otevřený")
                open_ports.append(port)
            else:
                print(f"❌ Port {port} je zavřený")
                
        except Exception as e:
            print(f"❌ Port {port} - chyba: {e}")
    
    return open_ports

async def main():
    print("🚂 OpenTTD Debug - Hledání problému s připojením")
    print("=" * 60)
    print(f"Server: {config.SERVER_IP}:{config.ADMIN_PORT}")
    print(f"Bot: {config.BOT_NAME}")
    print()
    
    # 1. Sken portů
    open_ports = await scan_nearby_ports()
    print(f"\n📊 Otevřené porty: {open_ports}")
    
    # 2. Raw připojení
    await test_raw_connection()
    
    # 3. Test různých hesel
    working_password = await test_different_passwords()
    
    # 4. Test sync vs async
    await test_sync_vs_async()
    
    print("\n" + "=" * 60)
    print("📋 Shrnutí:")
    
    if open_ports:
        print(f"✅ Otevřené porty: {open_ports}")
    else:
        print("❌ Žádné porty nejsou otevřené")
    
    if working_password is not None:
        print(f"✅ Funkční heslo nalezeno")
    else:
        print("❌ Žádné heslo nefunguje")
    
    print("\n💡 Doporučení:")
    print("1. Zkontrolujte, že OpenTTD server má povolený admin port")
    print("2. Ověřte konfiguraci serveru (openttd.cfg)")
    print("3. Možná server nepovoluje admin připojení")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Přerušeno")
        sys.exit(1)
